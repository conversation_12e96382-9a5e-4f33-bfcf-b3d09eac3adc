<?php

if (!defined('ABSPATH')) { exit; }

/*

Sınav Denemeleri Say<PERSON>sı - Tutor Entegrasyonu
* @version 1.0.0

Bu template dosyası düzenlenebilir ve kendi özel template'iniz ile değiştirilebilir. Bunun için bu dosyayı tema (veya child tema) klasörünüz altında 'marketking' adlı bir klasöre kopyalayın ve orada düzenleyin.

Örneğin, temanız storefront ise, bu dosyayı wp-content/themes/storefront/marketking/ altına kopyalayabilir ve kendi özel içeriğinizle düzenleyebilirsiniz.

*/

// Tutor eklentisinin yüklü olup olmadığını kontrol et
if (!function_exists('tutor') || !current_user_can(tutor()->instructor_role)) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Bu sayfaya erişim için Tutor eklentisinin yüklü olması ve eğitmen yetkisine sahip olmanız gerekmektedir.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

use TUTOR\Input;
use Tutor\Models\QuizModel;

// Kullanıcı ID'sini al
$current_user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()) {
    $current_user_id = marketking()->get_team_member_parent();
}

// Tek deneme detayını görüntüle
if (isset($_GET['view_quiz_attempt_id'])) {
    $attempt_id = intval($_GET['view_quiz_attempt_id']);
    // Burada tek deneme detayı gösterilecek - şimdilik basit bir mesaj
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="nk-block-head nk-block-head-sm">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title page-title"><?php esc_html_e('Sınav Deneme Detayı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                <div class="nk-block-des text-soft">
                                    <p><?php printf(esc_html__('Deneme ID: %d', 'marketking-multivendor-marketplace-for-woocommerce'), $attempt_id); ?></p>
                                </div>
                            </div>
                            <div class="nk-block-head-content">
                                <a href="<?php echo esc_url(remove_query_arg('view_quiz_attempt_id')); ?>" class="btn btn-outline-light">
                                    <em class="icon ni ni-arrow-left"></em>
                                    <span><?php esc_html_e('Geri Dön', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block">
                        <div class="card">
                            <div class="card-inner">
                                <p><?php esc_html_e('Sınav deneme detayları burada gösterilecek.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

$item_per_page = tutor_utils()->get_option('pagination_per_page', 10);
$current_page = max(1, Input::get('current_page', 1, Input::TYPE_INT));
$offset = ($current_page - 1) * $item_per_page;

// Filtre parametreleri
$course_filter = Input::get('course-id', '');
$order_filter = Input::get('order', 'DESC');
$date_filter = Input::get('date', '');

$course_id = tutor_utils()->get_assigned_courses_ids_by_instructors();
$quiz_attempts = QuizModel::get_quiz_attempts($offset, $item_per_page, '', $course_filter, $date_filter, $order_filter, null, false, true);
$quiz_attempts_count = QuizModel::get_quiz_attempts($offset, $item_per_page, '', $course_filter, $date_filter, $order_filter, null, true, true);

// Kursları al (eğitmenin kursları)
$courses = tutor_utils()->get_courses_for_instructors($current_user_id);
?>

<div class="nk-content marketking_sinav_denemeleri_page">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h4 class="nk-block-title page-title"><?php esc_html_e('Sınav Denemeleri', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                            <div class="nk-block-des text-soft">
                                <p><?php esc_html_e('Öğrencilerinizin sınav denemelerini görüntüleyebilir ve değerlendirebilirsiniz.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtreler -->
                <div class="nk-block">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <form method="GET" class="form-validate">
                                <div class="row g-4">
                                    <div class="col-lg-4">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Kurslar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <select name="course-id" class="form-select" data-search="on">
                                                <option value=""><?php esc_html_e('Tüm Kurslar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                <?php if ($courses) : ?>
                                                    <?php foreach ($courses as $course) : ?>
                                                        <option value="<?php echo esc_attr($course->ID); ?>" <?php selected($course_filter, $course->ID); ?>>
                                                            <?php echo esc_html($course->post_title); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                <?php else : ?>
                                                    <option value=""><?php esc_html_e('Kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Sıralama', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <select name="order" class="form-select">
                                                <option value="DESC" <?php selected($order_filter, 'DESC'); ?>><?php esc_html_e('Yeniden Eskiye', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                <option value="ASC" <?php selected($order_filter, 'ASC'); ?>><?php esc_html_e('Eskiden Yeniye', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <input type="date" name="date" class="form-control" value="<?php echo esc_attr($date_filter); ?>">
                                        </div>
                                    </div>
                                    <div class="col-lg-2">
                                        <div class="form-group">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn-primary d-block"><?php esc_html_e('Filtrele', 'marketking-multivendor-marketplace-for-woocommerce'); ?></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sınav Denemeleri Listesi -->
                <div class="nk-block">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <?php if (empty($quiz_attempts)) : ?>
                                <div class="text-center py-4">
                                    <em class="icon ni ni-file-check" style="font-size: 3rem; opacity: 0.3;"></em>
                                    <h6 class="text-soft"><?php esc_html_e('Henüz sınav denemesi bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                    <p class="text-soft"><?php esc_html_e('Öğrencileriniz sınavlara girmeye başladığında burada görüntülenecektir.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                </div>
                            <?php else : ?>
                                <div class="nk-tb-list nk-tb-ulist">
                                    <div class="nk-tb-item nk-tb-head">
                                        <div class="nk-tb-col"><span class="sub-text"><?php esc_html_e('Öğrenci', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                        <div class="nk-tb-col tb-col-md"><span class="sub-text"><?php esc_html_e('Sınav', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                        <div class="nk-tb-col tb-col-lg"><span class="sub-text"><?php esc_html_e('Kurs', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                        <div class="nk-tb-col tb-col-md"><span class="sub-text"><?php esc_html_e('Puan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                        <div class="nk-tb-col tb-col-lg"><span class="sub-text"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                        <div class="nk-tb-col tb-col-md"><span class="sub-text"><?php esc_html_e('Durum', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                        <div class="nk-tb-col nk-tb-col-tools text-right"></div>
                                    </div>
                                    
                                    <?php foreach ($quiz_attempts as $attempt) : ?>
                                        <?php
                                        $student = get_userdata($attempt->user_id);
                                        $quiz = get_post($attempt->quiz_id);
                                        $course = tutor_utils()->get_course_by_quiz($attempt->quiz_id);
                                        $earned_marks = $attempt->earned_marks;
                                        $total_marks = $attempt->total_marks;
                                        $percentage = $total_marks > 0 ? round(($earned_marks / $total_marks) * 100, 2) : 0;
                                        
                                        // Durum belirleme
                                        $status = 'completed';
                                        $status_text = __('Tamamlandı', 'marketking-multivendor-marketplace-for-woocommerce');
                                        $status_class = 'text-success';
                                        
                                        if ($attempt->attempt_status === 'review_required') {
                                            $status = 'review';
                                            $status_text = __('İnceleme Gerekli', 'marketking-multivendor-marketplace-for-woocommerce');
                                            $status_class = 'text-warning';
                                        }
                                        ?>
                                        <div class="nk-tb-item">
                                            <div class="nk-tb-col">
                                                <div class="user-card">
                                                    <div class="user-avatar bg-primary">
                                                        <span><?php echo esc_html(strtoupper(substr($student->display_name, 0, 2))); ?></span>
                                                    </div>
                                                    <div class="user-info">
                                                        <span class="tb-lead"><?php echo esc_html($student->display_name); ?></span>
                                                        <span class="fs-12px text-soft"><?php echo esc_html($student->user_email); ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="nk-tb-col tb-col-md">
                                                <span class="tb-amount"><?php echo esc_html($quiz->post_title); ?></span>
                                            </div>
                                            <div class="nk-tb-col tb-col-lg">
                                                <span class="tb-amount"><?php echo esc_html($course->post_title); ?></span>
                                            </div>
                                            <div class="nk-tb-col tb-col-md">
                                                <span class="tb-amount">
                                                    <?php echo esc_html($earned_marks . '/' . $total_marks); ?>
                                                    <span class="text-soft">(%<?php echo esc_html($percentage); ?>)</span>
                                                </span>
                                            </div>
                                            <div class="nk-tb-col tb-col-lg">
                                                <span class="tb-amount"><?php echo date_i18n('d.m.Y H:i', strtotime($attempt->attempt_ended_at)); ?></span>
                                            </div>
                                            <div class="nk-tb-col tb-col-md">
                                                <span class="tb-status <?php echo esc_attr($status_class); ?>"><?php echo esc_html($status_text); ?></span>
                                            </div>
                                            <div class="nk-tb-col nk-tb-col-tools">
                                                <ul class="nk-tb-actions gx-1">
                                                    <li class="nk-tb-action-hidden">
                                                        <a href="<?php echo esc_url(add_query_arg('view_quiz_attempt_id', $attempt->attempt_id)); ?>" 
                                                           class="btn btn-trigger btn-icon" 
                                                           data-toggle="tooltip" 
                                                           data-placement="top" 
                                                           title="<?php esc_attr_e('Detayları Görüntüle', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                            <em class="icon ni ni-eye"></em>
                                                        </a>
                                                    </li>
                                                    <?php if ($status === 'review') : ?>
                                                        <li class="nk-tb-action-hidden">
                                                            <a href="#" class="btn btn-trigger btn-icon" data-toggle="tooltip" data-placement="top" title="<?php esc_attr_e('İncele ve Puanla', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                                <em class="icon ni ni-edit"></em>
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>
                                                    <li>
                                                        <div class="drodown">
                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                            <div class="dropdown-menu dropdown-menu-right">
                                                                <ul class="link-list-opt no-bdr">
                                                                    <li>
                                                                        <a href="<?php echo esc_url(add_query_arg('view_quiz_attempt_id', $attempt->attempt_id)); ?>">
                                                                            <em class="icon ni ni-eye"></em>
                                                                            <span><?php esc_html_e('Detayları Görüntüle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                        </a>
                                                                    </li>
                                                                    <?php if ($status === 'review') : ?>
                                                                        <li>
                                                                            <a href="#">
                                                                                <em class="icon ni ni-edit"></em>
                                                                                <span><?php esc_html_e('İncele ve Puanla', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                            </a>
                                                                        </li>
                                                                    <?php endif; ?>
                                                                    <li>
                                                                        <a href="#">
                                                                            <em class="icon ni ni-download"></em>
                                                                            <span><?php esc_html_e('Rapor İndir', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                        </a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <!-- Sayfalama -->
                                <?php
                                $total_pages = ceil($quiz_attempts_count / $item_per_page);
                                if ($total_pages > 1) {
                                    ?>
                                    <div class="card-inner">
                                        <div class="nk-block-between-md g-3">
                                            <div class="g">
                                                <ul class="pagination justify-content-center justify-content-md-start">
                                                    <?php if ($current_page > 1) : ?>
                                                        <li class="page-item">
                                                            <a class="page-link" href="<?php echo esc_url(add_query_arg('current_page', $current_page - 1)); ?>">
                                                                <?php esc_html_e('Önceki', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>
                                                    
                                                    <?php for ($i = 1; $i <= $total_pages; $i++) : ?>
                                                        <li class="page-item<?php echo ($i == $current_page) ? ' active' : ''; ?>">
                                                            <a class="page-link" href="<?php echo esc_url(add_query_arg('current_page', $i)); ?>"><?php echo $i; ?></a>
                                                        </li>
                                                    <?php endfor; ?>
                                                    
                                                    <?php if ($current_page < $total_pages) : ?>
                                                        <li class="page-item">
                                                            <a class="page-link" href="<?php echo esc_url(add_query_arg('current_page', $current_page + 1)); ?>">
                                                                <?php esc_html_e('Sonraki', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                }
                                ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
